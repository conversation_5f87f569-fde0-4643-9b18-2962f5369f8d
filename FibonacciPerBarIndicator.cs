//
// Copyright (C) 2025, NinjaTrader LLC <www.ninjatrader.com>.
// NinjaTrader reserves the right to modify or overwrite this NinjaScript component with each release.
//
#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds indicators in this folder and is required. Do not change it.
namespace NinjaTrader.NinjaScript.Indicators
{
	public class FibonacciPerBarIndicator : Indicator
	{
		private double[] fibLevels = { 1.1, 1.08, 1.0, 0.9, 0.1, 0.0, -0.08, -0.1 };
		private Brush[] fibColors;

		// Multi-timeframe variables
		private DateTime lastMtfBarTime;
		private bool isNewMtfBar;
		private double mtfHigh, mtfLow, mtfOpen, mtfClose;
		private bool mtfDataInitialized;
		private int lastProcessedBar = -1;

		// MTF Candle display variables
		private List<string> mtfCandleObjects = new List<string>();
		private const int MaxMtfCandles = 50;
		
		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description					= @"Fibonacci levels drawn per bar based on high/low range";
				Name						= "FibonacciPerBarIndicator";
				Calculate					= Calculate.OnBarClose;
				IsOverlay					= true;
				DisplayInDataBox			= true;
				DrawOnPricePanel			= true;
				DrawHorizontalGridLines		= true;
				DrawVerticalGridLines		= true;
				PaintPriceMarkers			= true;
				ScaleJustification			= NinjaTrader.Gui.Chart.ScaleJustification.Right;
				//Disable this property if your indicator requires custom values that cumulate with each new market data event. 
				//See Help Guide for additional information.
				IsSuspendedWhileInactive	= true;
				
				// Input parameters
				ShowLevels					= true;
				ShowLabels					= true;
				LineWidth					= 1;
				LineTransparency			= 20;

				// Multi-timeframe parameters
				UseMtfMode					= false;
				MtfTimeframe				= 5;
				MtfPeriodType				= BarsPeriodType.Minute;
				UseConfirmedData			= true;

				// MTF Candle display parameters
				ShowMtfCandles				= false;
				ShowCandleInfo				= false;
				CandleBullishColor			= Brushes.Green;
				CandleBearishColor			= Brushes.Red;
				CandleWickColor				= Brushes.White;
				CandleBorderColor			= Brushes.Black;
				CandleTransparency			= 20;
				
				// Color inputs
				FibColor11					= Brushes.White;
				FibColor108					= Brushes.White;
				FibColor10					= Brushes.Green;
				FibColor09					= Brushes.Orange;
				FibColor01					= Brushes.Purple;
				FibColor00					= Brushes.Red;
				FibColorNeg08				= Brushes.White;
				FibColorNeg1				= Brushes.White;
				TextColor					= Brushes.White;
			}
			else if (State == State.Configure)
			{
				// Add multi-timeframe data series if MTF mode is enabled
				if (UseMtfMode)
				{
					try
					{
						AddDataSeries(MtfPeriodType, MtfTimeframe);
						Print($"Added MTF data series: {MtfTimeframe} {MtfPeriodType}");
					}
					catch (Exception ex)
					{
						Print($"Error adding MTF data series: {ex.Message}");
					}
				}
			}
			else if (State == State.DataLoaded)
			{
				// Initialize color array
				fibColors = new Brush[]
				{
					FibColor11, FibColor108, FibColor10, FibColor09,
					FibColor01, FibColor00, FibColorNeg08, FibColorNeg1
				};

				// Reset MTF state for chart switching
				ResetMtfState();
			}
			else if (State == State.Terminated)
			{
				// Clean up when indicator is removed
				ResetMtfState();
			}
		}

		protected override void OnBarUpdate()
		{
			if (CurrentBar < 1)
				return;

			// Handle multi-timeframe mode
			if (UseMtfMode)
			{
				// Only process primary bars (chart timeframe) for drawing
				if (BarsInProgress != 0)
					return;

				// Check if we have enough bars on both timeframes
				if (CurrentBars[0] < 1 || (BarsArray.Length > 1 && CurrentBars[1] < 1))
					return;

				// Validate MTF data series exists
				if (BarsArray.Length < 2)
				{
					Print("Error: MTF data series not properly initialized");
					return;
				}

				// Reset state if we're processing a different bar than before (chart switching)
				if (lastProcessedBar != CurrentBar)
				{
					if (!mtfDataInitialized)
					{
						ResetMtfState();
						mtfDataInitialized = true;
					}
					lastProcessedBar = CurrentBar;
				}

				// Detect new MTF bar
				DateTime currentMtfTime = Times[1][0];
				isNewMtfBar = currentMtfTime != lastMtfBarTime;

				if (isNewMtfBar)
				{
					lastMtfBarTime = currentMtfTime;

					// Get MTF OHLC data with validation
					int mtfIndex = UseConfirmedData ? 1 : 0; // Use previous bar if confirmed data is enabled
					if (CurrentBars[1] >= mtfIndex)
					{
						try
						{
							mtfOpen = Opens[1][mtfIndex];
							mtfHigh = Highs[1][mtfIndex];
							mtfLow = Lows[1][mtfIndex];
							mtfClose = Closes[1][mtfIndex];

							// Validate MTF data
							if (mtfHigh <= 0 || mtfLow <= 0 || mtfHigh < mtfLow)
							{
								Print($"Invalid MTF data: High={mtfHigh}, Low={mtfLow}");
								return;
							}
						}
						catch (Exception ex)
						{
							Print($"Error accessing MTF data: {ex.Message}");
							return;
						}
					}
					else
					{
						return; // Not enough MTF data yet
					}
				}
				else if (!UseConfirmedData)
				{
					// Update current developing MTF bar data for real-time mode
					try
					{
						mtfOpen = Opens[1][0];
						mtfHigh = Highs[1][0];
						mtfLow = Lows[1][0];
						mtfClose = Closes[1][0];

						// Validate real-time MTF data
						if (mtfHigh <= 0 || mtfLow <= 0 || mtfHigh < mtfLow)
						{
							return; // Skip invalid data
						}
					}
					catch (Exception ex)
					{
						Print($"Error accessing real-time MTF data: {ex.Message}");
						return;
					}
				}
				else
				{
					return; // No new MTF bar and using confirmed data
				}

				// Calculate price range from MTF data
				double rangeHigh = mtfHigh;
				double rangeLow = mtfLow;
				double priceRange = rangeHigh - rangeLow;

				if (priceRange <= 0)
					return;

				// Draw Fibonacci levels for MTF mode
				DrawFibonacciLevels(rangeLow, priceRange, true);

				// Draw MTF candle if enabled
				if (ShowMtfCandles && isNewMtfBar)
				{
					DrawMtfCandle();
				}
			}
			else
			{
				// Original per-bar mode
				// Calculate price range for current bar
				double rangeHigh = High[0];
				double rangeLow = Low[0];
				double priceRange = rangeHigh - rangeLow;

				if (priceRange <= 0)
					return;

				// Draw Fibonacci levels for per-bar mode
				DrawFibonacciLevels(rangeLow, priceRange, false);
			}
		}

		private void ResetMtfState()
		{
			lastMtfBarTime = DateTime.MinValue;
			isNewMtfBar = false;
			mtfHigh = mtfLow = mtfOpen = mtfClose = 0;
			mtfDataInitialized = false;
			lastProcessedBar = -1;

			// Clean up any existing MTF candle objects
			foreach (string objectName in mtfCandleObjects)
			{
				try
				{
					RemoveDrawObject(objectName);
				}
				catch
				{
					// Ignore errors if object doesn't exist
				}
			}
			mtfCandleObjects.Clear();
		}

		private void DrawFibonacciLevels(double rangeLow, double priceRange, bool isMtfMode)
		{
			if (!ShowLevels)
				return;

			for (int i = 0; i < fibLevels.Length; i++)
			{
				double fibLevel = fibLevels[i];
				double fibPrice = rangeLow + (priceRange * fibLevel);

				// Create unique line names for MTF vs per-bar mode
				string lineName = isMtfMode ?
					$"MTFFibLine_{lastMtfBarTime.Ticks}_{i}" :
					$"FibLine_{CurrentBar}_{i}";

				// For MTF mode, draw lines spanning the MTF timeframe duration
				if (isMtfMode)
				{
					// Calculate the number of chart bars in the MTF timeframe
					int mtfDurationBars = CalculateMtfDurationInBars();

					// Draw line spanning the MTF period
					Draw.Line(this, lineName, mtfDurationBars, fibPrice, 0, fibPrice,
						GetColorWithTransparency(fibColors[i], LineTransparency));
				}
				else
				{
					// Original per-bar mode: line for current bar only
					Draw.Line(this, lineName, 0, fibPrice, 1, fibPrice,
						GetColorWithTransparency(fibColors[i], LineTransparency));
				}

				// Add label if enabled
				if (ShowLabels)
				{
					string labelName = isMtfMode ?
						$"MTFFibLabel_{lastMtfBarTime.Ticks}_{i}" :
						$"FibLabel_{CurrentBar}_{i}";
					string labelText = $"{fibLevel:F2} ({fibPrice:F3})";
					Draw.Text(this, labelName, labelText, 0, fibPrice, TextColor);
				}
			}
		}

		private int CalculateMtfDurationInBars()
		{
			if (!UseMtfMode || BarsArray.Length < 2)
				return 1;

			try
			{
				// Calculate approximate number of chart bars in one MTF bar
				// This is a simplified calculation - in practice, this can vary
				TimeSpan chartBarDuration = GetBarDuration(BarsArray[0].BarsPeriod);
				TimeSpan mtfBarDuration = GetBarDuration(BarsArray[1].BarsPeriod);

				if (chartBarDuration.TotalSeconds > 0 && mtfBarDuration.TotalSeconds > 0)
				{
					double ratio = mtfBarDuration.TotalSeconds / chartBarDuration.TotalSeconds;
					int barsInMtf = (int)Math.Round(ratio);

					// Ensure reasonable bounds
					return Math.Max(1, Math.Min(barsInMtf, 1000));
				}
			}
			catch (Exception ex)
			{
				Print($"Error calculating MTF duration: {ex.Message}");
			}

			return 1;
		}

		private TimeSpan GetBarDuration(BarsPeriod barsPeriod)
		{
			switch (barsPeriod.BarsPeriodType)
			{
				case BarsPeriodType.Second:
					return TimeSpan.FromSeconds(barsPeriod.Value);
				case BarsPeriodType.Minute:
					return TimeSpan.FromMinutes(barsPeriod.Value);
				case BarsPeriodType.Day:
					return TimeSpan.FromDays(barsPeriod.Value);
				case BarsPeriodType.Week:
					return TimeSpan.FromDays(barsPeriod.Value * 7);
				case BarsPeriodType.Month:
					return TimeSpan.FromDays(barsPeriod.Value * 30); // Approximate
				default:
					return TimeSpan.FromMinutes(1); // Default fallback
			}
		}

		private void DrawMtfCandle()
		{
			if (!UseMtfMode || mtfHigh <= 0 || mtfLow <= 0 || mtfHigh < mtfLow)
				return;

			// Clean up old candles first
			CleanupOldMtfCandles();

			// Calculate MTF duration in bars for positioning
			int mtfDurationBars = CalculateMtfDurationInBars();

			// Determine candle color based on open/close relationship
			bool isBullish = mtfClose > mtfOpen;
			Brush candleColor = isBullish ? CandleBullishColor : CandleBearishColor;

			// Calculate candle body coordinates
			double bodyTop = Math.Max(mtfOpen, mtfClose);
			double bodyBottom = Math.Min(mtfOpen, mtfClose);

			// Create unique names for this MTF candle
			string candleBodyName = $"MTFCandleBody_{lastMtfBarTime.Ticks}";
			string upperWickName = $"MTFUpperWick_{lastMtfBarTime.Ticks}";
			string lowerWickName = $"MTFLowerWick_{lastMtfBarTime.Ticks}";
			string candleInfoName = $"MTFCandleInfo_{lastMtfBarTime.Ticks}";

			// Draw candle body using rectangle
			Draw.Rectangle(this, candleBodyName, false, mtfDurationBars - 1, bodyBottom, 0, bodyTop,
				GetColorWithTransparency(CandleBorderColor, 0),
				GetColorWithTransparency(candleColor, CandleTransparency), CandleTransparency);
			mtfCandleObjects.Add(candleBodyName);

			// Calculate wick position (center of the MTF period)
			int wickPosition = mtfDurationBars / 2;

			// Draw upper wick if it exists
			if (mtfHigh > bodyTop)
			{
				Draw.Line(this, upperWickName, wickPosition, bodyTop, wickPosition, mtfHigh,
					GetColorWithTransparency(CandleWickColor, 0));
				mtfCandleObjects.Add(upperWickName);
			}

			// Draw lower wick if it exists
			if (mtfLow < bodyBottom)
			{
				Draw.Line(this, lowerWickName, wickPosition, bodyBottom, wickPosition, mtfLow,
					GetColorWithTransparency(CandleWickColor, 0));
				mtfCandleObjects.Add(lowerWickName);
			}

			// Add candle info label if enabled
			if (ShowCandleInfo)
			{
				double priceRange = mtfHigh - mtfLow;
				string candleInfo = $"MTF {MtfTimeframe}{GetPeriodTypeShortName(MtfPeriodType)} Candle\n" +
								   $"O: {mtfOpen:F2}\n" +
								   $"H: {mtfHigh:F2}\n" +
								   $"L: {mtfLow:F2}\n" +
								   $"C: {mtfClose:F2}\n" +
								   $"Range: {priceRange:F2}";

				// Position label above the candle
				double labelY = mtfHigh + (priceRange * 0.02);
				Draw.Text(this, candleInfoName, candleInfo, wickPosition, labelY, TextColor);
				mtfCandleObjects.Add(candleInfoName);
			}
		}

		private void CleanupOldMtfCandles()
		{
			// Remove old candle objects if we exceed the maximum
			while (mtfCandleObjects.Count > MaxMtfCandles * 4) // 4 objects per candle (body, 2 wicks, label)
			{
				string oldObjectName = mtfCandleObjects[0];
				mtfCandleObjects.RemoveAt(0);

				// Try to remove the drawing object
				try
				{
					RemoveDrawObject(oldObjectName);
				}
				catch
				{
					// Ignore errors if object doesn't exist
				}
			}
		}

		private string GetPeriodTypeShortName(BarsPeriodType periodType)
		{
			switch (periodType)
			{
				case BarsPeriodType.Second: return "s";
				case BarsPeriodType.Minute: return "m";
				case BarsPeriodType.Day: return "d";
				case BarsPeriodType.Week: return "w";
				case BarsPeriodType.Month: return "M";
				default: return "";
			}
		}

		private Brush GetColorWithTransparency(Brush originalBrush, int transparency)
		{
			if (originalBrush is SolidColorBrush solidBrush)
			{
				Color color = solidBrush.Color;
				byte alpha = (byte)(255 * (100 - transparency) / 100);
				return new SolidColorBrush(Color.FromArgb(alpha, color.R, color.G, color.B));
			}
			return originalBrush;
		}

		#region Properties
		[NinjaScriptProperty]
		[Display(Name="Use Multi-Timeframe Mode", Description="Enable multi-timeframe Fibonacci levels", Order=1, GroupName="Multi-Timeframe Settings")]
		public bool UseMtfMode
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="MTF Timeframe Value", Description="Multi-timeframe period value", Order=2, GroupName="Multi-Timeframe Settings")]
		public int MtfTimeframe
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="MTF Period Type", Description="Multi-timeframe period type", Order=3, GroupName="Multi-Timeframe Settings")]
		public BarsPeriodType MtfPeriodType
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Use Confirmed Data", Description="Use confirmed MTF data (no repaint)", Order=4, GroupName="Multi-Timeframe Settings")]
		public bool UseConfirmedData
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Show MTF Candles", Description="Display the multi-timeframe candle that Fibonacci levels are based on", Order=5, GroupName="Multi-Timeframe Settings")]
		public bool ShowMtfCandles
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Show Candle Info", Description="Show MTF candle OHLC information", Order=6, GroupName="Multi-Timeframe Settings")]
		public bool ShowCandleInfo
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Show Levels", Description="Show Fibonacci Levels", Order=1, GroupName="Display Settings")]
		public bool ShowLevels
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Show Labels", Description="Show Level Labels", Order=2, GroupName="Display Settings")]
		public bool ShowLabels
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, 5)]
		[Display(Name="Line Width", Description="Line Width", Order=3, GroupName="Display Settings")]
		public int LineWidth
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 100)]
		[Display(Name="Line Transparency", Description="Line Transparency", Order=4, GroupName="Display Settings")]
		public int LineTransparency
		{ get; set; }

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="1.1 Level Color", Description="1.1 Level Color", Order=1, GroupName="Colors")]
		public Brush FibColor11
		{ get; set; }

		[Browsable(false)]
		public string FibColor11Serializable
		{
			get { return Serialize.BrushToString(FibColor11); }
			set { FibColor11 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="1.08 Level Color", Description="1.08 Level Color", Order=2, GroupName="Colors")]
		public Brush FibColor108
		{ get; set; }

		[Browsable(false)]
		public string FibColor108Serializable
		{
			get { return Serialize.BrushToString(FibColor108); }
			set { FibColor108 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="1.0 Level Color", Description="1.0 Level Color", Order=3, GroupName="Colors")]
		public Brush FibColor10
		{ get; set; }

		[Browsable(false)]
		public string FibColor10Serializable
		{
			get { return Serialize.BrushToString(FibColor10); }
			set { FibColor10 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="0.9 Level Color", Description="0.9 Level Color", Order=4, GroupName="Colors")]
		public Brush FibColor09
		{ get; set; }

		[Browsable(false)]
		public string FibColor09Serializable
		{
			get { return Serialize.BrushToString(FibColor09); }
			set { FibColor09 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="0.1 Level Color", Description="0.1 Level Color", Order=5, GroupName="Colors")]
		public Brush FibColor01
		{ get; set; }

		[Browsable(false)]
		public string FibColor01Serializable
		{
			get { return Serialize.BrushToString(FibColor01); }
			set { FibColor01 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="0.0 Level Color", Description="0.0 Level Color", Order=6, GroupName="Colors")]
		public Brush FibColor00
		{ get; set; }

		[Browsable(false)]
		public string FibColor00Serializable
		{
			get { return Serialize.BrushToString(FibColor00); }
			set { FibColor00 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="-0.08 Level Color", Description="-0.08 Level Color", Order=7, GroupName="Colors")]
		public Brush FibColorNeg08
		{ get; set; }

		[Browsable(false)]
		public string FibColorNeg08Serializable
		{
			get { return Serialize.BrushToString(FibColorNeg08); }
			set { FibColorNeg08 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="-0.1 Level Color", Description="-0.1 Level Color", Order=8, GroupName="Colors")]
		public Brush FibColorNeg1
		{ get; set; }

		[Browsable(false)]
		public string FibColorNeg1Serializable
		{
			get { return Serialize.BrushToString(FibColorNeg1); }
			set { FibColorNeg1 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="Label Text Color", Description="Label Text Color", Order=9, GroupName="Colors")]
		public Brush TextColor
		{ get; set; }

		[Browsable(false)]
		public string TextColorSerializable
		{
			get { return Serialize.BrushToString(TextColor); }
			set { TextColor = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="Bullish Candle Color", Description="Color for bullish MTF candles", Order=1, GroupName="MTF Candle Colors")]
		public Brush CandleBullishColor
		{ get; set; }

		[Browsable(false)]
		public string CandleBullishColorSerializable
		{
			get { return Serialize.BrushToString(CandleBullishColor); }
			set { CandleBullishColor = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="Bearish Candle Color", Description="Color for bearish MTF candles", Order=2, GroupName="MTF Candle Colors")]
		public Brush CandleBearishColor
		{ get; set; }

		[Browsable(false)]
		public string CandleBearishColorSerializable
		{
			get { return Serialize.BrushToString(CandleBearishColor); }
			set { CandleBearishColor = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="Candle Wick Color", Description="Color for MTF candle wicks", Order=3, GroupName="MTF Candle Colors")]
		public Brush CandleWickColor
		{ get; set; }

		[Browsable(false)]
		public string CandleWickColorSerializable
		{
			get { return Serialize.BrushToString(CandleWickColor); }
			set { CandleWickColor = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="Candle Border Color", Description="Color for MTF candle borders", Order=4, GroupName="MTF Candle Colors")]
		public Brush CandleBorderColor
		{ get; set; }

		[Browsable(false)]
		public string CandleBorderColorSerializable
		{
			get { return Serialize.BrushToString(CandleBorderColor); }
			set { CandleBorderColor = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[Range(0, 100)]
		[Display(Name="Candle Transparency", Description="Transparency level for MTF candle fill", Order=5, GroupName="MTF Candle Colors")]
		public int CandleTransparency
		{ get; set; }
		#endregion
	}
}
